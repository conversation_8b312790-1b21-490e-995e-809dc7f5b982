import 'package:uuid/uuid.dart';
import '../../models/transaction_model.dart';
import '../../models/parse_result.dart';
import '../../utils/currency_utils.dart';
import '../storage_service.dart';
import 'category_finder_service.dart';
import 'learned_association_service.dart';

/// Fallback transaction parser that uses regex-based parsing when ML Kit is not available
class FallbackParserService {
  final CategoryFinderService _categoryFinder;
  final StorageService _storageService;
  LearnedAssociationService? _learnedAssociationService;
  final Uuid _uuid = const Uuid();

  FallbackParserService(StorageService storageService)
      : _categoryFinder = CategoryFinderService(storageService),
        _storageService = storageService {
    _initializeLearnedAssociationService(storageService);
  }

  /// Initialize the learned association service
  void _initializeLearnedAssociationService(StorageService storageService) async {
    try {
      _learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
    } catch (e) {
      print('Failed to initialize learned association service in FallbackParserService: $e');
    }
  }

  /// Parse transaction using regex-based approach (original parser logic)
  Future<ParseResult> parseTransaction(String text) async {
    try {
      // Normalize text for easier parsing
      final normalizedText = text.toLowerCase().trim();
      
      // Try to extract amount and currency first to detect negative amounts
      final amountResult = _extractAmount(normalizedText);
      if (amountResult == null || amountResult['amount'] == null) {
        return ParseResult.failed(
          _createFallbackTransaction(text),
          'Could not extract amount from text'
        );
      }

      final amount = amountResult['amount'] as double;
      final isNegativeAmount = amountResult['isNegative'] as bool? ?? false;
      String currencyCode = amountResult['currency'] as String? ?? await _storageService.getDefaultCurrency();

      // Try to detect transaction type with negative amount information
      final detectedType = _detectTransactionType(normalizedText, isNegativeAmount: isNegativeAmount);

      // If transaction type is unclear, return needsType status
      if (detectedType == null) {
        // Create partial transaction with default expense type for type disambiguation
        final partialTransaction = Transaction(
          id: _uuid.v4(),
          amount: amount,
          type: TransactionType.expense, // Default type, will be updated by user selection
          categoryId: 'other',
          date: DateTime.now(),
          description: _createDescription(text),
          tags: _extractTags(text),
          currencyCode: currencyCode,
        );

        return ParseResult.needsType(partialTransaction);
      }

      // Find category using the category finder service
      final categoryId = await _categoryFinder.findCategory(text, detectedType);

      // Create the transaction
      final transaction = Transaction(
        id: _uuid.v4(),
        amount: amount,
        type: detectedType,
        categoryId: categoryId ?? 'other', // Use default category if none found
        date: DateTime.now(),
        description: _createDescription(text),
        tags: _extractTags(text),
        currencyCode: currencyCode,
      );

      // Return result indicating if category selection is needed
      if (categoryId == null) {
        return ParseResult.needsCategory(transaction);
      } else {
        return ParseResult.success(transaction);
      }

    } catch (e) {
      return ParseResult.failed(
        _createFallbackTransaction(text),
        'Parsing failed: $e'
      );
    }
  }

  /// Learn a category association for future use
  Future<void> learnCategory(String text, String categoryId) async {
    // Use new unified service if available
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(text, categoryId: categoryId);
    } else {
      // Fall back to category finder for backward compatibility
      await _categoryFinder.learnCategory(text, categoryId);
    }
  }

  /// Detect transaction type from text
  TransactionType? _detectTransactionType(String text, {bool isNegativeAmount = false}) {
    // Check for negative sign or minus at the beginning which indicates expense
    if (RegExp(r'^\s*-').hasMatch(text) || isNegativeAmount) {
      return TransactionType.expense;
    }
    
    // Income patterns (check these first as they're more specific)
    if (RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward|refund.*from)')
        .hasMatch(text)) {
      return TransactionType.income;
    }
    
    // Loan patterns
    if (RegExp(r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to|borrow)')
        .hasMatch(text)) {
      return TransactionType.loan;
    }
    
    // Expense patterns (more general, check after specific income/loan patterns)
    if (RegExp(r'(spent|paid|bought|purchased|expense|pay(?!ment received)|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)')
        .hasMatch(text)) {
      return TransactionType.expense;
    }
    
    // Special case: 'for' keyword typically indicates expense unless preceded by income keyword
    if (RegExp(r'\bfor\b').hasMatch(text) && 
        !RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b').hasMatch(text)) {
      return TransactionType.expense;
    }
    
    // Default to expense if contains $ or other currency symbols
    if (RegExp(r'[$€£¥₹]').hasMatch(text)) {
      return TransactionType.expense;
    }
    
    return null;
  }
  
  /// Extract amount from text
  Map<String, dynamic>? _extractAmount(String text) {
    // Handle negative amount pattern first (like -500$ for toys)
    bool isNegative = false;
    String processedText = text;

    if (text.startsWith('-')) {
      isNegative = true;
      // Remove the leading minus for easier parsing
      processedText = text.substring(1).trim();
    }

    // Extract positive amount from processed text
    final result = _extractPositiveAmount(processedText);
    if (result != null) {
      // Add negative flag to the result
      result['isNegative'] = isNegative;
    }

    return result;
  }
  
  /// Helper to extract positive amount values from text
  Map<String, dynamic>? _extractPositiveAmount(String text) {
    // Fixed regex to properly handle large numbers without comma issues
    // Simplified to handle any number of digits with optional commas and decimals
    final amountRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)?\s?(\d+(?:,\d{3})*(?:\.\d{1,2})?)\s?(?:dollars|USD|euros?|EUR|pounds?|GBP|yen|JPY|yuan|CNY|rupees?|INR|rubles?|RUB|won|KRW|pesos?|MXN|PHP|dong|VND|baht|THB|lira|TRY|shekel|ILS|reais?|BRL|SGD|HKD|AUD|CAD|NZD|\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪)?');
    final match = amountRegex.firstMatch(text);

    if (match != null) {
      final currencySymbol = match.group(1);
      final amountString = match.group(2)!.replaceAll(',', ''); // Remove commas for parsing
      final amount = double.tryParse(amountString);

      if (amount != null) {
        String? currency = _extractCurrency(text);
        if (currency == null && currencySymbol != null) {
          currency = CurrencyUtils.symbolToCurrencyCode(currencySymbol, context: text);
        }

        return {
          'amount': amount,
          'currency': currency,
        };
      }
    }

    return null;
  }

  /// Extract currency information from text
  String? _extractCurrency(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }
    
    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }
    
    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// Create description from text
  String _createDescription(String text) {
    return text.trim();
  }

  /// Extract tags from text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);
    
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }
    
    return tags;
  }

  /// Create fallback transaction when parsing fails
  Transaction _createFallbackTransaction(String text, {double? amount}) {
    return Transaction(
      id: _uuid.v4(),
      amount: amount ?? 0.0,
      type: TransactionType.expense,
      categoryId: 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: 'USD',
    );
  }
}
