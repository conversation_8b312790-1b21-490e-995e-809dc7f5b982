import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/services/parser/learned_association_service.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_storage_service.dart';

void main() {
  group('Transaction Model Tests', () {
    
    group('Transaction Creation', () {
      test('should create transaction with all required fields', () {
        final date = DateTime.now();
        final transaction = Transaction(
          id: 'test-id',
          amount: 25.50,
          type: TransactionType.expense,
          categoryId: 'food',
          date: date,
          description: 'Coffee at Starbucks',
          tags: ['coffee', 'morning'],
          currencyCode: 'USD',
        );

        expect(transaction.id, equals('test-id'));
        expect(transaction.amount, equals(25.50));
        expect(transaction.type, equals(TransactionType.expense));
        expect(transaction.categoryId, equals('food'));
        expect(transaction.date, equals(date));
        expect(transaction.description, equals('Coffee at Starbucks'));
        expect(transaction.tags, equals(['coffee', 'morning']));
        expect(transaction.currencyCode, equals('USD'));
      });

      test('should use default values for optional fields', () {
        final transaction = Transaction(
          id: 'test-id',
          amount: 100.0,
          type: TransactionType.expense,
          categoryId: 'test-category',
          date: DateTime.now(),
          description: 'Test transaction',
        );

        expect(transaction.tags, equals([]));
        expect(transaction.currencyCode, equals('USD'));
      });

      test('should create transaction with test helper', () {
        final transaction = TestHelpers.createTestTransaction(
          amount: 50.0,
          type: TransactionType.income,
          description: 'Test income',
        );

        expect(transaction.amount, equals(50.0));
        expect(transaction.type, equals(TransactionType.income));
        expect(transaction.description, equals('Test income'));
        expect(transaction.currencyCode, equals('USD'));
      });
    });

    group('Transaction JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final date = DateTime(2023, 12, 25, 14, 30, 0);
        final transaction = Transaction(
          id: 'test-id',
          amount: 25.50,
          type: TransactionType.expense,
          categoryId: 'food',
          date: date,
          description: 'Coffee',
          tags: ['coffee'],
          currencyCode: 'EUR',
        );

        final json = transaction.toJson();

        expect(json['id'], equals('test-id'));
        expect(json['amount'], equals(25.50));
        expect(json['type'], equals('expense'));
        expect(json['categoryId'], equals('food'));
        expect(json['date'], equals(date.toIso8601String()));
        expect(json['description'], equals('Coffee'));
        expect(json['tags'], equals(['coffee']));
        expect(json['currencyCode'], equals('EUR'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'test-id',
          'amount': 25.50,
          'type': 'expense',
          'categoryId': 'food',
          'date': '2023-12-25T14:30:00.000Z',
          'description': 'Coffee',
          'tags': ['coffee'],
          'currencyCode': 'EUR',
        };

        final transaction = Transaction.fromJson(json);

        expect(transaction.id, equals('test-id'));
        expect(transaction.amount, equals(25.50));
        expect(transaction.type, equals(TransactionType.expense));
        expect(transaction.categoryId, equals('food'));
        expect(transaction.date, equals(DateTime.parse('2023-12-25T14:30:00.000Z')));
        expect(transaction.description, equals('Coffee'));
        expect(transaction.tags, equals(['coffee']));
        expect(transaction.currencyCode, equals('EUR'));
      });

      test('should handle missing optional fields in JSON', () {
        final json = {
          'id': 'test-id',
          'amount': 100.0,
          'type': 'income',
          'categoryId': 'salary',
          'date': '2023-12-25T14:30:00.000Z',
          'description': 'Monthly salary',
        };

        final transaction = Transaction.fromJson(json);

        expect(transaction.tags, equals([]));
        expect(transaction.currencyCode, equals('USD'));
      });

      test('should handle null tags in JSON', () {
        final json = {
          'id': 'test-id',
          'amount': 100.0,
          'type': 'expense',
          'categoryId': 'food',
          'date': '2023-12-25T14:30:00.000Z',
          'description': 'Lunch',
          'tags': null,
        };

        final transaction = Transaction.fromJson(json);
        expect(transaction.tags, equals([]));
      });

      test('should round-trip correctly through JSON', () {
        final original = TestHelpers.createTestTransaction(
          amount: 123.45,
          type: TransactionType.loan,
          tags: ['personal', 'friend'],
          currencyCode: 'GBP',
        );

        final json = original.toJson();
        final restored = Transaction.fromJson(json);

        expect(TestAssertions.transactionsEqual(original, restored), isTrue);
      });
    });

    group('Transaction copyWith', () {
      late Transaction originalTransaction;

      setUp(() {
        originalTransaction = TestHelpers.createTestTransaction(
          amount: 100.0,
          type: TransactionType.expense,
          description: 'Original description',
          tags: ['tag1', 'tag2'],
          currencyCode: 'USD',
        );
      });

      test('should create copy with updated amount', () {
        final updated = originalTransaction.copyWith(amount: 200.0);

        expect(updated.amount, equals(200.0));
        expect(updated.id, equals(originalTransaction.id));
        expect(updated.type, equals(originalTransaction.type));
        expect(updated.description, equals(originalTransaction.description));
      });

      test('should create copy with updated type', () {
        final updated = originalTransaction.copyWith(type: TransactionType.income);

        expect(updated.type, equals(TransactionType.income));
        expect(updated.amount, equals(originalTransaction.amount));
        expect(updated.description, equals(originalTransaction.description));
      });

      test('should create copy with updated description', () {
        final updated = originalTransaction.copyWith(description: 'New description');

        expect(updated.description, equals('New description'));
        expect(updated.amount, equals(originalTransaction.amount));
        expect(updated.type, equals(originalTransaction.type));
      });

      test('should create copy with updated tags', () {
        final newTags = ['new1', 'new2', 'new3'];
        final updated = originalTransaction.copyWith(tags: newTags);

        expect(updated.tags, equals(newTags));
        expect(updated.amount, equals(originalTransaction.amount));
        expect(updated.type, equals(originalTransaction.type));
      });

      test('should create copy with updated currency', () {
        final updated = originalTransaction.copyWith(currencyCode: 'EUR');

        expect(updated.currencyCode, equals('EUR'));
        expect(updated.amount, equals(originalTransaction.amount));
        expect(updated.type, equals(originalTransaction.type));
      });

      test('should create copy with multiple updated fields', () {
        final newDate = DateTime.now().add(const Duration(days: 1));
        final updated = originalTransaction.copyWith(
          amount: 250.0,
          type: TransactionType.income,
          description: 'Updated description',
          date: newDate,
          currencyCode: 'EUR',
        );

        expect(updated.amount, equals(250.0));
        expect(updated.type, equals(TransactionType.income));
        expect(updated.description, equals('Updated description'));
        expect(updated.date, equals(newDate));
        expect(updated.currencyCode, equals('EUR'));
        expect(updated.id, equals(originalTransaction.id));
        expect(updated.categoryId, equals(originalTransaction.categoryId));
      });

      test('should create identical copy when no fields updated', () {
        final copy = originalTransaction.copyWith();

        expect(TestAssertions.transactionsEqual(originalTransaction, copy), isTrue);
      });
    });

    group('TransactionType Enum', () {
      test('should have correct enum values', () {
        expect(TransactionType.values, contains(TransactionType.expense));
        expect(TransactionType.values, contains(TransactionType.income));
        expect(TransactionType.values, contains(TransactionType.loan));
        expect(TransactionType.values.length, equals(3));
      });

      test('should serialize enum to string correctly', () {
        expect(TransactionType.expense.name, equals('expense'));
        expect(TransactionType.income.name, equals('income'));
        expect(TransactionType.loan.name, equals('loan'));
      });

      test('should deserialize enum from string correctly', () {
        expect(TransactionType.values.byName('expense'), equals(TransactionType.expense));
        expect(TransactionType.values.byName('income'), equals(TransactionType.income));
        expect(TransactionType.values.byName('loan'), equals(TransactionType.loan));
      });
    });

    group('Edge Cases and Validation', () {
      test('should handle zero amount', () {
        final transaction = TestHelpers.createTestTransaction(amount: 0.0);
        expect(transaction.amount, equals(0.0));
      });

      test('should handle very large amounts', () {
        final transaction = TestHelpers.createTestTransaction(amount: 999999.99);
        expect(transaction.amount, equals(999999.99));
      });

      test('should handle very small amounts', () {
        final transaction = TestHelpers.createTestTransaction(amount: 0.01);
        expect(transaction.amount, equals(0.01));
      });

      test('should handle empty description', () {
        final transaction = TestHelpers.createTestTransaction(description: '');
        expect(transaction.description, equals(''));
      });

      test('should handle very long description', () {
        final longDescription = 'A' * 1000;
        final transaction = TestHelpers.createTestTransaction(description: longDescription);
        expect(transaction.description, equals(longDescription));
      });

      test('should handle special characters in description', () {
        const specialDescription = 'Café lunch 🍕💰 !@#\$%^&*()';
        final transaction = TestHelpers.createTestTransaction(description: specialDescription);
        expect(transaction.description, equals(specialDescription));
      });

      test('should handle empty tags list', () {
        final transaction = TestHelpers.createTestTransaction(tags: []);
        expect(transaction.tags, isEmpty);
      });

      test('should handle many tags', () {
        final manyTags = List.generate(50, (index) => 'tag$index');
        final transaction = TestHelpers.createTestTransaction(tags: manyTags);
        expect(transaction.tags, equals(manyTags));
      });

      test('should handle special characters in tags', () {
        final specialTags = ['café', '🍕', 'special-tag', 'tag_with_underscore'];
        final transaction = TestHelpers.createTestTransaction(tags: specialTags);
        expect(transaction.tags, equals(specialTags));
      });
    });

    group('Different Currency Codes', () {
      test('should handle various currency codes', () {
        final currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'INR', 'KRW'];
        
        for (final currency in currencies) {
          final transaction = TestHelpers.createTestTransaction(currencyCode: currency);
          expect(transaction.currencyCode, equals(currency));
        }
      });

      test('should handle case sensitivity in currency codes', () {
        final transaction = TestHelpers.createTestTransaction(currencyCode: 'usd');
        expect(transaction.currencyCode, equals('usd'));
      });
    });
  });

  group('Category Model Tests', () {
    
    group('Category Creation', () {
      test('should create category with all fields', () {
        final category = Category(
          id: 'food',
          name: 'Food & Dining',
          icon: '🍽️',
          colorValue: 0xFF2196F3,
          type: TransactionType.expense,
        );

        expect(category.id, equals('food'));
        expect(category.name, equals('Food & Dining'));
        expect(category.icon, equals('🍽️'));
        expect(category.colorValue, equals(0xFF2196F3));
        expect(category.type, equals(TransactionType.expense));
      });

      test('should create category with test helper', () {
        final category = TestHelpers.createTestCategory(
          name: 'Transportation',
          icon: '🚗',
          type: TransactionType.expense,
        );

        expect(category.name, equals('Transportation'));
        expect(category.icon, equals('🚗'));
        expect(category.type, equals(TransactionType.expense));
      });
    });

    group('Category JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final category = Category(
          id: 'food',
          name: 'Food & Dining',
          icon: '🍽️',
          colorValue: 0xFF2196F3,
          type: TransactionType.expense,
        );

        final json = category.toJson();

        expect(json['id'], equals('food'));
        expect(json['name'], equals('Food & Dining'));
        expect(json['icon'], equals('🍽️'));
        expect(json['colorValue'], equals(0xFF2196F3));
        expect(json['type'], equals('expense'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'food',
          'name': 'Food & Dining',
          'icon': '🍽️',
          'colorValue': 0xFF2196F3,
          'type': 'expense',
        };

        final category = Category.fromJson(json);

        expect(category.id, equals('food'));
        expect(category.name, equals('Food & Dining'));
        expect(category.icon, equals('🍽️'));
        expect(category.colorValue, equals(0xFF2196F3));
        expect(category.type, equals(TransactionType.expense));
      });

      test('should round-trip correctly through JSON', () {
        final original = TestHelpers.createTestCategory(
          name: 'Entertainment',
          icon: '🎬',
          type: TransactionType.expense,
        );

        final json = original.toJson();
        final restored = Category.fromJson(json);

        expect(TestAssertions.categoriesEqual(original, restored), isTrue);
      });
    });

    group('Category Types', () {
      test('should work with expense categories', () {
        final category = TestHelpers.createTestCategory(type: TransactionType.expense);
        expect(category.type, equals(TransactionType.expense));
      });

      test('should work with income categories', () {
        final category = TestHelpers.createTestCategory(type: TransactionType.income);
        expect(category.type, equals(TransactionType.income));
      });

      test('should work with loan categories', () {
        final category = TestHelpers.createTestCategory(type: TransactionType.loan);
        expect(category.type, equals(TransactionType.loan));
      });
    });

    group('Category Edge Cases', () {
      test('should handle empty name', () {
        final category = TestHelpers.createTestCategory(name: '');
        expect(category.name, equals(''));
      });

      test('should handle very long name', () {
        final longName = 'A' * 100;
        final category = TestHelpers.createTestCategory(name: longName);
        expect(category.name, equals(longName));
      });

      test('should handle special characters in name', () {
        const specialName = 'Café & Restaurant 🍽️';
        final category = TestHelpers.createTestCategory(name: specialName);
        expect(category.name, equals(specialName));
      });

      test('should handle empty icon', () {
        final category = TestHelpers.createTestCategory(icon: '');
        expect(category.icon, equals(''));
      });

      test('should handle various color values', () {
        final colors = [0x00000000, 0xFFFFFFFF, 0xFF2196F3, 0x80FF0000];
        
        for (final color in colors) {
          final category = TestHelpers.createTestCategory(colorValue: color);
          expect(category.colorValue, equals(color));
        }
      });
    });
  });

  group('Test Helper Validation', () {
    test('should create valid test categories', () {
      final categories = TestHelpers.getTestCategories();
      
      expect(categories, isNotEmpty);
      for (final category in categories) {
        expect(category.id, isNotEmpty);
        expect(category.name, isNotEmpty);
        expect(category.icon, isNotEmpty);
        expect(TransactionType.values, contains(category.type));
      }
    });

    test('should create valid test transactions', () {
      final transactions = TestHelpers.getTestTransactionScenarios();
      
      expect(transactions, isNotEmpty);
      for (final transaction in transactions) {
        expect(transaction.id, isNotEmpty);
        expect(transaction.amount, greaterThanOrEqualTo(0.0));
        expect(TransactionType.values, contains(transaction.type));
        expect(transaction.description, isNotEmpty);
        expect(transaction.currencyCode, isNotEmpty);
      }
    });
  });

  group('ChatMessage Tests', () {

    group('ChatMessage Creation', () {
      test('should create user message with required fields', () {
        final timestamp = DateTime.now();
        final message = ChatMessage(
          id: 'msg-1',
          text: 'I spent \$25 on coffee',
          isUserMessage: true,
          timestamp: timestamp,
          type: ChatMessageType.user,
        );

        expect(message.id, equals('msg-1'));
        expect(message.text, equals('I spent \$25 on coffee'));
        expect(message.isUserMessage, isTrue);
        expect(message.timestamp, equals(timestamp));
        expect(message.type, equals(ChatMessageType.user));
        expect(message.quickReplies, isNull);
        expect(message.quickReplyId, isNull);
      });

      test('should create system message with required fields', () {
        final timestamp = DateTime.now();
        final message = ChatMessage(
          id: 'msg-2',
          text: 'Transaction saved successfully!',
          isUserMessage: false,
          timestamp: timestamp,
          type: ChatMessageType.system,
        );

        expect(message.id, equals('msg-2'));
        expect(message.text, equals('Transaction saved successfully!'));
        expect(message.isUserMessage, isFalse);
        expect(message.timestamp, equals(timestamp));
        expect(message.type, equals(ChatMessageType.system));
        expect(message.quickReplies, isNull);
        expect(message.quickReplyId, isNull);
      });

      test('should create system message with quick replies', () {
        final timestamp = DateTime.now();
        final quickReplies = ['Expense', 'Income', 'Loan'];
        final message = ChatMessage(
          id: 'msg-3',
          text: 'What type of transaction is this?',
          isUserMessage: false,
          timestamp: timestamp,
          type: ChatMessageType.systemWithQuickReplies,
          quickReplies: quickReplies,
          quickReplyId: 'type-selection',
        );

        expect(message.id, equals('msg-3'));
        expect(message.text, equals('What type of transaction is this?'));
        expect(message.isUserMessage, isFalse);
        expect(message.timestamp, equals(timestamp));
        expect(message.type, equals(ChatMessageType.systemWithQuickReplies));
        expect(message.quickReplies, equals(quickReplies));
        expect(message.quickReplyId, equals('type-selection'));
      });
    });

    group('ChatMessage Factory Constructors', () {
      test('should create user message using factory', () {
        final timestamp = DateTime.now();
        final message = ChatMessage.user(
          id: 'user-msg-1',
          text: 'I bought groceries for \$50',
          timestamp: timestamp,
        );

        expect(message.text, equals('I bought groceries for \$50'));
        expect(message.isUserMessage, isTrue);
        expect(message.type, equals(ChatMessageType.user));
        expect(message.id, equals('user-msg-1'));
        expect(message.timestamp, equals(timestamp));
        expect(message.quickReplies, isNull);
        expect(message.quickReplyId, isNull);
      });

      test('should create system message using factory', () {
        final timestamp = DateTime.now();
        final message = ChatMessage.system(
          id: 'system-msg-1',
          text: 'I found a transaction for \$50. Please select a category.',
          timestamp: timestamp,
        );

        expect(message.text, equals('I found a transaction for \$50. Please select a category.'));
        expect(message.isUserMessage, isFalse);
        expect(message.type, equals(ChatMessageType.system));
        expect(message.id, equals('system-msg-1'));
        expect(message.timestamp, equals(timestamp));
        expect(message.quickReplies, isNull);
        expect(message.quickReplyId, isNull);
      });

      test('should create system message with quick replies using factory', () {
        final timestamp = DateTime.now();
        final quickReplies = ['Food', 'Transportation', 'Entertainment'];
        final message = ChatMessage.systemWithQuickReplies(
          id: 'quick-reply-msg-1',
          text: 'Please select a category:',
          timestamp: timestamp,
          quickReplyId: 'category-selection',
          quickReplies: quickReplies,
        );

        expect(message.text, equals('Please select a category:'));
        expect(message.isUserMessage, isFalse);
        expect(message.type, equals(ChatMessageType.systemWithQuickReplies));
        expect(message.quickReplies, equals(quickReplies));
        expect(message.quickReplyId, equals('category-selection'));
        expect(message.id, equals('quick-reply-msg-1'));
        expect(message.timestamp, equals(timestamp));
      });
    });

    group('ChatMessageType Enum Tests', () {
      test('should have all expected enum values', () {
        expect(ChatMessageType.values, hasLength(3));
        expect(ChatMessageType.values, contains(ChatMessageType.user));
        expect(ChatMessageType.values, contains(ChatMessageType.system));
        expect(ChatMessageType.values, contains(ChatMessageType.systemWithQuickReplies));
      });

      test('should handle enum comparisons correctly', () {
        final timestamp = DateTime.now();
        final userMessage = ChatMessage.user(
          id: 'test-user',
          text: 'Test',
          timestamp: timestamp,
        );
        final systemMessage = ChatMessage.system(
          id: 'test-system',
          text: 'Test',
          timestamp: timestamp,
        );
        final quickReplyMessage = ChatMessage.systemWithQuickReplies(
          id: 'test-quick-reply',
          text: 'Test',
          timestamp: timestamp,
          quickReplyId: 'test',
          quickReplies: ['Option 1'],
        );

        expect(userMessage.type, equals(ChatMessageType.user));
        expect(systemMessage.type, equals(ChatMessageType.system));
        expect(quickReplyMessage.type, equals(ChatMessageType.systemWithQuickReplies));

        expect(userMessage.type != systemMessage.type, isTrue);
        expect(systemMessage.type != quickReplyMessage.type, isTrue);
      });
    });

    group('Quick Reply Validation', () {
      test('should handle empty quick replies list', () {
        final timestamp = DateTime.now();
        final message = ChatMessage.systemWithQuickReplies(
          id: 'empty-test-msg',
          text: 'Choose an option:',
          timestamp: timestamp,
          quickReplyId: 'empty-test',
          quickReplies: [],
        );

        expect(message.quickReplies, equals([]));
        expect(message.quickReplyId, equals('empty-test'));
        expect(message.type, equals(ChatMessageType.systemWithQuickReplies));
      });

      test('should handle quick replies with special characters', () {
        final timestamp = DateTime.now();
        final quickReplies = ['🍕 Food & Dining', 'Transportation 🚗', 'Entertainment (Movies)'];
        final message = ChatMessage.systemWithQuickReplies(
          id: 'special-chars-msg',
          text: 'Select category:',
          timestamp: timestamp,
          quickReplyId: 'special-chars',
          quickReplies: quickReplies,
        );

        expect(message.quickReplies, equals(quickReplies));
        expect(message.quickReplies![0], contains('🍕'));
        expect(message.quickReplies![1], contains('🚗'));
        expect(message.quickReplies![2], contains('(Movies)'));
      });

      test('should handle long quick reply options', () {
        final timestamp = DateTime.now();
        final longOptions = [
          'This is a very long category name that might wrap to multiple lines',
          'Another extremely long option that tests the UI boundaries',
          'Short',
        ];
        final message = ChatMessage.systemWithQuickReplies(
          id: 'long-options-msg',
          text: 'Choose:',
          timestamp: timestamp,
          quickReplyId: 'long-options',
          quickReplies: longOptions,
        );

        expect(message.quickReplies, equals(longOptions));
        expect(message.quickReplies![0].length, greaterThan(50));
        expect(message.quickReplies![2].length, lessThan(10));
      });
    });

    group('Manual Edit Learning Functionality', () {
      late MockStorageService mockStorage;
      late TransactionProvider provider;

      setUp(() async {
        mockStorage = MockStorageService();
        await mockStorage.init();
        provider = TransactionProvider(mockStorage);
        // Wait for learned association service to be initialized
        await provider.waitForLearnedAssociationService();
      });

      tearDown(() async {
        // Clean up learned associations
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        await learnedService.clearAllData();
        LearnedAssociationService.resetInstance();
      });

      test('should trigger learning when transaction type is changed', () async {
        // Create original transaction
        final originalTransaction = TestHelpers.createTestTransaction(
          description: 'salary payment',
          type: TransactionType.expense, // Wrong type initially
          categoryId: 'work',
        );

        // Add transaction to provider first
        provider.addTransaction(originalTransaction);

        // Create updated transaction with correct type
        final updatedTransaction = originalTransaction.copyWith(
          type: TransactionType.income, // Corrected type
        );

        // Update transaction - should trigger learning
        await provider.updateTransaction(updatedTransaction);

        // Verify learning occurred
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        final association = await learnedService.getAssociation('salary payment');

        expect(association, isNotNull);
        expect(association!.type, equals(TransactionType.income));
        expect(association.categoryId, equals('work'));
      });

      test('should trigger learning when transaction category is changed', () async {
        // Create original transaction
        final originalTransaction = TestHelpers.createTestTransaction(
          description: 'coffee at starbucks',
          type: TransactionType.expense,
          categoryId: 'shopping', // Wrong category initially
        );

        // Add transaction to provider first
        provider.addTransaction(originalTransaction);

        // Create updated transaction with correct category
        final updatedTransaction = originalTransaction.copyWith(
          categoryId: 'food', // Corrected category
        );

        // Update transaction - should trigger learning
        await provider.updateTransaction(updatedTransaction);

        // Verify learning occurred
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        final association = await learnedService.getAssociation('coffee at starbucks');

        expect(association, isNotNull);
        expect(association!.type, equals(TransactionType.expense));
        expect(association.categoryId, equals('food'));
      });

      test('should trigger learning when both type and category are changed', () async {
        // Create original transaction
        final originalTransaction = TestHelpers.createTestTransaction(
          description: 'freelance project payment',
          type: TransactionType.expense, // Wrong type
          categoryId: 'shopping', // Wrong category
        );

        // Add transaction to provider first
        provider.addTransaction(originalTransaction);

        // Create updated transaction with correct values
        final updatedTransaction = originalTransaction.copyWith(
          type: TransactionType.income, // Corrected type
          categoryId: 'work', // Corrected category
        );

        // Update transaction - should trigger learning
        await provider.updateTransaction(updatedTransaction);

        // Verify learning occurred
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        final association = await learnedService.getAssociation('freelance project payment');

        expect(association, isNotNull);
        expect(association!.type, equals(TransactionType.income));
        expect(association.categoryId, equals('work'));
      });

      test('should not trigger learning when no type or category changes', () async {
        // Create original transaction
        final originalTransaction = TestHelpers.createTestTransaction(
          description: 'grocery shopping',
          type: TransactionType.expense,
          categoryId: 'food',
        );

        // Add transaction to provider first
        provider.addTransaction(originalTransaction);

        // Create updated transaction with only amount change
        final updatedTransaction = originalTransaction.copyWith(
          amount: 150.0, // Only amount changed
        );

        // Update transaction - should NOT trigger learning
        await provider.updateTransaction(updatedTransaction);

        // Verify no learning occurred
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        final association = await learnedService.getAssociation('grocery shopping');

        expect(association, isNull);
      });

      test('should not trigger learning for empty description', () async {
        // Create original transaction with empty description
        final originalTransaction = TestHelpers.createTestTransaction(
          description: '', // Empty description
          type: TransactionType.expense,
          categoryId: 'shopping',
        );

        // Add transaction to provider first
        provider.addTransaction(originalTransaction);

        // Create updated transaction with different category
        final updatedTransaction = originalTransaction.copyWith(
          categoryId: 'food',
        );

        // Update transaction - should NOT trigger learning due to empty description
        await provider.updateTransaction(updatedTransaction);

        // Verify no learning occurred
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        final allAssociations = await learnedService.getAllAssociations();

        expect(allAssociations.length, equals(0));
      });

      test('should handle learning errors gracefully during transaction update', () async {
        // Create transactions
        final originalTransaction = TestHelpers.createTestTransaction(
          description: 'test transaction',
          type: TransactionType.expense,
          categoryId: 'shopping',
        );

        // Add transaction to provider first
        provider.addTransaction(originalTransaction);

        final updatedTransaction = originalTransaction.copyWith(
          categoryId: 'food',
        );

        // Corrupt the storage to simulate learning error
        await mockStorage.setString('learned_associations', 'invalid json');

        // Update should not throw even if learning fails
        expect(() async => await provider.updateTransaction(updatedTransaction),
            returnsNormally);
      });

      test('should increment confidence on repeated manual edits', () async {
        final originalTransaction = TestHelpers.createTestTransaction(
          description: 'amazon purchase',
          type: TransactionType.expense,
          categoryId: 'shopping',
        );

        // Add transaction to provider first
        provider.addTransaction(originalTransaction);

        // First edit - change to entertainment
        final firstUpdate = originalTransaction.copyWith(categoryId: 'entertainment');
        await provider.updateTransaction(firstUpdate);

        // Second edit - change to food first, then back to entertainment to trigger learning again
        final secondUpdate = firstUpdate.copyWith(categoryId: 'food');
        await provider.updateTransaction(secondUpdate);

        final thirdUpdate = secondUpdate.copyWith(categoryId: 'entertainment');
        await provider.updateTransaction(thirdUpdate);

        // Verify confidence increased
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        final association = await learnedService.getAssociation('amazon purchase');

        expect(association, isNotNull);
        expect(association!.categoryId, equals('entertainment'));
        expect(association.confidence, equals(3)); // Each edit increments confidence
      });

      test('should handle text normalization in learning', () async {
        final originalTransaction = TestHelpers.createTestTransaction(
          description: 'Café! @#\$ Coffee & More...', // Text with special characters
          type: TransactionType.expense,
          categoryId: 'shopping',
        );

        // Add transaction to provider first
        provider.addTransaction(originalTransaction);

        final updatedTransaction = originalTransaction.copyWith(
          categoryId: 'food',
        );

        await provider.updateTransaction(updatedTransaction);

        // Should be able to find with normalized text
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        final association = await learnedService.getAssociation('cafe coffee more');

        expect(association, isNotNull);
        expect(association!.categoryId, equals('food'));
      });
    });
  });
}
